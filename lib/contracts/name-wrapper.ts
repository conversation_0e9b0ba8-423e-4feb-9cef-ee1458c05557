// Name Wrapper Contract ABI and Configuration for ENS v2 Wrapper
// Migrated from crefy-connect-backend/src/ens/ContractABIs/NameWrapper.ts

export const NAMEWRAPPER_CONTRACT_ABI = [
    {
        "inputs": [
          {
            "internalType": "address",
            "name": "from",
            "type": "address"
          },
          {
            "internalType": "address",
            "name": "to",
            "type": "address"
          },
          {
            "internalType": "uint256",
            "name": "id",
            "type": "uint256"
          },
          {
            "internalType": "uint256",
            "name": "amount",
            "type": "uint256"
          },
          {
            "internalType": "bytes",
            "name": "data",
            "type": "bytes"
          }
        ],
        "name": "safeTransferFrom",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function"
      },
      {
        "inputs": [
          {
            "internalType": "address",
            "name": "account",
            "type": "address"
          },
          {
            "internalType": "uint256",
            "name": "id",
            "type": "uint256"
          }
        ],
        "name": "balanceOf",
        "outputs": [
          {
            "internalType": "uint256",
            "name": "",
            "type": "uint256"
          }
        ],
        "stateMutability": "view",
        "type": "function"
      },
      {
        "inputs": [
          {
            "internalType": "uint256",
            "name": "id",
            "type": "uint256"
          }
        ],
        "name": "ownerOf",
        "outputs": [
          {
            "internalType": "address",
            "name": "owner",
            "type": "address"
          }
        ],
        "stateMutability": "view",
        "type": "function"
      }
    ] as const;

// TypeScript types for better type safety
export type NameWrapperContractABI = typeof NAMEWRAPPER_CONTRACT_ABI;

// Contract configuration for different networks
export const NAMEWRAPPER_CONTRACT_CONFIG = {
	mainnet: {
		address: "0xD4416b13d2b3a9aBae7AcD5D6C2BbDBE25686401" as const,
		abi: NAMEWRAPPER_CONTRACT_ABI,
		chainId: 1
	},
	sepolia: {
		address: "0x0635513f179D50A207757E05759CbD106d7dFcE8" as const,
		abi: NAMEWRAPPER_CONTRACT_ABI,
		chainId: 11155111
	},
	holesky: {
		address: "0xab50971078225D365994dc1Edcb9b7FD72Bb4862" as const,
		abi: NAMEWRAPPER_CONTRACT_ABI,
		chainId: 17000
	},
	goerli: {
		address: "0x0635513f179D50A207757E05759CbD106d7dFcE8" as const, // Same as sepolia for backward compatibility
		abi: NAMEWRAPPER_CONTRACT_ABI,
		chainId: 5
	}
} as const;

// Supported chains for name wrapper contract
export type SupportedNameWrapperChain = keyof typeof NAMEWRAPPER_CONTRACT_CONFIG;

// Helper function to get NameWrapper contract address for a specific chain
export function getNameWrapperAddress(chain: string): string {
	const chainKey = chain.toLowerCase() as SupportedNameWrapperChain;
	const config = NAMEWRAPPER_CONTRACT_CONFIG[chainKey];

	if (!config) {
		throw new Error(`NameWrapper contract not supported on chain: ${chain}`);
	}

	return config.address;
}

// Helper function to get NameWrapper contract config for a specific chain
export function getNameWrapperConfig(chain: string) {
	const chainKey = chain.toLowerCase() as SupportedNameWrapperChain;
	const config = NAMEWRAPPER_CONTRACT_CONFIG[chainKey];

	if (!config) {
		throw new Error(`NameWrapper contract not supported on chain: ${chain}`);
	}

	return config;
}

// Legacy export for backward compatibility (defaults to sepolia)
export const NAMEWRAPPER_CONTRACT_ADDRESS = NAMEWRAPPER_CONTRACT_CONFIG.sepolia.address;
