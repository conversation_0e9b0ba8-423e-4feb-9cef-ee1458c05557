# ENS Transfer Functions Implementation Guide

## Overview

This document describes the complete implementation of ENS transfer functions using the Crefy Connect backend API endpoints with wagmi hooks for secure blockchain interactions.

## Architecture

### 1. Backend API Integration

The implementation uses the Crefy Connect backend API endpoints exclusively to prepare transaction data:

**Primary**: Crefy Connect API endpoints (`/ens/prepare-registrar` and `/ens/namewrapper/prepare-transfer`)
**Execution**: Wagmi hooks for secure contract interactions
**Validation**: Automatic contract address correction in API service

### 2. API Endpoint Integration

The system uses the Crefy Connect backend API endpoints with automatic contract address validation:

```typescript
// Backend API Integration
const response = await apiService.prepareRegistrarTransaction({
  ensName: selectedENSName,
  chain: selectedChain
}, token, appId);

if (response.success && response.data?.data) {
  const transactionData = response.data.data;

  // Use backend-prepared transaction data with wagmi
  writeFactoryContract({
    address: transactionData.to, // Backend provides correct Factory contract address
    abi: FACTORY_CONTRACT_ABI,
    functionName: 'createSubnameRegistrar',
    args: [namehash(selectedENSName.toLowerCase())],
  });
}

// NameWrapper Transfer
const response = await apiService.prepareNameWrapperTransfer({
  chain: selectedChain,
  from: ensOwner,
  to: newOwnerAddress,
  id: tokenId,
  amount: "1",
  data: "0x"
}, token, appId);

writeNameWrapperContract({
  address: transactionData.to, // Backend provides correct NameWrapper contract address
  abi: NAMEWRAPPER_CONTRACT_ABI,
  functionName: 'safeTransferFrom',
  args: [ensOwner, newOwnerAddress, BigInt(tokenId), BigInt(1), '0x'],
});
```

### 2. Wagmi Integration

Using modern wagmi v2 hooks for contract interactions:

- `useWriteContract()` - For contract function calls
- `useWaitForTransactionReceipt()` - For transaction confirmation
- `usePublicClient()` - For reading blockchain state

### 3. Transaction Lifecycle

1. **Preparation**: Calculate function parameters (namehash, tokenId)
2. **Execution**: Call `writeContract` with proper ABI and args
3. **Confirmation**: Wait for transaction receipt using `useWaitForTransactionReceipt`
4. **Success/Error Handling**: Update UI state and show appropriate feedback

## Key Components

### Contract Addresses (Sepolia Testnet)

```typescript
FACTORY_CONTRACT_ADDRESS = "******************************************"
NAMEWRAPPER_CONTRACT_ADDRESS = "0x0635513f179D50A207757E05759CbD106d7dFcE8"
```

### Contract Functions

#### Factory Contract - `createSubnameRegistrar`
- **Purpose**: Creates a new subname registrar contract for an ENS domain
- **Parameters**: `bytes32 _parentNode` (namehash of the ENS name)
- **Returns**: `address` (address of the created registrar contract)

#### NameWrapper Contract - `safeTransferFrom`
- **Purpose**: Transfers wrapped ENS names between addresses
- **Parameters**: 
  - `address from` - Current owner
  - `address to` - New owner
  - `uint256 id` - Token ID (namehash as BigInt)
  - `uint256 amount` - Amount (always 1 for ENS)
  - `bytes data` - Additional data (usually empty)

### Utility Functions

#### `namehash(name: string): string`
Calculates the ENS namehash for a given domain name:

```typescript
export function namehash(name: string): string {
  if (!name) return '0x0000000000000000000000000000000000000000000000000000000000000000';
  
  const labels = name.split('.');
  let hash = '0x0000000000000000000000000000000000000000000000000000000000000000';
  
  for (let i = labels.length - 1; i >= 0; i--) {
    const labelHash = ethers.keccak256(ethers.toUtf8Bytes(labels[i]));
    hash = ethers.keccak256(ethers.concat([hash, labelHash]));
  }
  
  return hash;
}
```

#### `getENSTokenId(ensName: string): string`
Converts ENS name to token ID for NameWrapper operations:

```typescript
export function getENSTokenId(ensName: string): string {
  const hash = namehash(ensName.toLowerCase());
  return BigInt(hash).toString();
}
```

## Implementation Details

### 1. State Management

```typescript
// Wagmi contract write hooks
const { 
  writeContract: writeFactoryContract, 
  data: factoryTxHash, 
  isPending: isFactoryPending,
  error: factoryError 
} = useWriteContract();

// Transaction receipt hooks
const { 
  data: factoryReceipt, 
  isLoading: isFactoryReceiptLoading,
  isSuccess: isFactorySuccess 
} = useWaitForTransactionReceipt({
  hash: factoryTxHash,
});
```

### 2. Transaction Status Tracking

```typescript
type TransferStatus = 'idle' | 'preparing' | 'confirming' | 'pending' | 'success' | 'error';

// Update status based on wagmi hook states
useEffect(() => {
  if (isFactoryPending) {
    setTransferStatus('confirming');
  } else if (factoryTxHash && isFactoryReceiptLoading) {
    setTransferStatus('pending');
    setTxHash(factoryTxHash);
  }
}, [isFactoryPending, factoryTxHash, isFactoryReceiptLoading]);
```

### 3. Success Handling

```typescript
useEffect(() => {
  if (isFactorySuccess && factoryReceipt && factoryTxHash) {
    setTransferStatus('success');
    setTxHash(factoryTxHash);

    showToast({
      type: 'success',
      title: 'Registrar Created Successfully',
      description: `Subname registrar for ${selectedENSName} has been created successfully`
    });

    onSuccess?.(factoryTxHash, 'registrar');
  }
}, [isFactorySuccess, factoryReceipt, factoryTxHash, selectedENSName, onSuccess, showToast]);
```

## Error Handling

### Common Error Types

1. **User Rejection**: `error.code === 'ACTION_REJECTED'`
2. **Insufficient Funds**: `error.message?.includes('insufficient funds')`
3. **Invalid Parameters**: Contract validation errors
4. **Network Issues**: RPC or connection problems

### Error Recovery

```typescript
catch (error: any) {
  console.error('Contract interaction error:', error);
  setTransferStatus('error');

  let errorMessage = 'Transaction failed';
  
  if (error.code === 'ACTION_REJECTED') {
    errorMessage = 'Transaction was rejected by user';
  } else if (error.message?.includes('insufficient funds')) {
    errorMessage = 'Insufficient funds for gas fees';
  } else if (error.message) {
    errorMessage = error.message;
  }

  setTransferError(errorMessage);
  onError?.(errorMessage);
}
```

## Testing

### Test Scenarios

1. **Registrar Creation**:
   - Connect wallet to Sepolia testnet
   - Select an application
   - Enter valid ENS name (e.g., "test.eth")
   - Confirm transaction in wallet
   - Verify transaction on Sepolia Etherscan

2. **NameWrapper Transfer**:
   - Ensure you own a wrapped ENS name
   - Enter valid recipient address
   - Confirm transfer transaction
   - Verify ownership change on blockchain

### Verification

- Transaction hashes should appear on the correct Etherscan (mainnet/sepolia/goerli)
- Contract events should be emitted correctly
- State changes should be reflected on-chain

## Benefits of This Implementation

1. **Reliability**: Direct contract interaction eliminates backend API dependencies
2. **Transparency**: All transaction data is prepared client-side and visible
3. **Security**: No reliance on potentially compromised backend endpoints
4. **Performance**: Faster execution without backend round-trips
5. **Debugging**: Clear error messages and transaction tracking

## Migration from Backend API

The previous implementation relied on backend endpoints:
- `POST /ens/prepare-registrar`
- `POST /ens/namewrapper/prepare-transfer`

These endpoints were returning invalid transaction data (targeting user addresses instead of contracts). The new implementation bypasses these issues by preparing transactions client-side using the contract ABIs.

## Recent Fixes Implemented

### 1. Toast Notification Limit

**Issue**: Too many toast notifications could overwhelm the UI during ENS operations.

**Solution**: Limited toast notifications to a maximum of 2 at any time:

```typescript
// lib/toast-context.tsx
const showToast = (toast: Omit<ToastProps, 'id' | 'onClose'>) => {
  const id = Math.random().toString(36).substr(2, 9);
  const newToast = { ...toast, id, onClose: () => removeToast(id) };

  setToasts(prev => {
    // Limit to maximum of 2 toasts
    const updatedToasts = [...prev, newToast];
    if (updatedToasts.length > 2) {
      // Remove the oldest toast(s) to keep only 2
      return updatedToasts.slice(-2);
    }
    return updatedToasts;
  });
};
```

### 2. Backend API Contract Address Correction

**Issue**: Backend API endpoints were returning user wallet addresses instead of contract addresses.

**Solution**: Automatic contract address correction in API service:

```typescript
// lib/api.ts
const response = await this.makeRequest<PrepareRegistrarResponse>('/ens/prepare-registrar', 'POST', data, headers);

if (response.success && response.data?.data) {
  const transactionData = response.data.data;
  const expectedFactoryAddress = "******************************************";

  // Check if backend returned incorrect contract address
  if (transactionData.to?.toLowerCase() !== expectedFactoryAddress.toLowerCase()) {
    console.warn('Backend returned incorrect contract address, correcting...');
    transactionData.to = expectedFactoryAddress; // Correct the address
  }
}
```

### 3. NameWrapper Transfer Function Fix

**Issue**: NameWrapper transfers were failing due to incorrect ABI usage and backend integration.

**Solution**: Proper ERC1155 `safeTransferFrom` implementation with hybrid backend/client-side approach:

```typescript
// Correct NameWrapper transfer
writeNameWrapperContract({
  address: NAMEWRAPPER_CONTRACT_ADDRESS,
  abi: NAMEWRAPPER_CONTRACT_ABI,
  functionName: 'safeTransferFrom',
  args: [
    ensOwner as `0x${string}`,
    newOwnerAddress as `0x${string}`,
    BigInt(tokenId),
    BigInt(1),
    '0x' as `0x${string}`
  ],
});
```

## Issues Resolved

### 1. Backend API Problems

**Previous Issue**: Backend endpoints were returning invalid transaction data:
```json
{
  "to": "0x70087881ae9cc6141288a35aCfc58a78735B531b",    // User's address (wrong!)
  "from": "0x70087881ae9cc6141288a35aCfc58a78735B531b",  // Same address
  "data": "0x33b9b37309ac8117090c1b5b4f4353078f15c0d1f934e0cfe222e032fe3ad43dffe4efea"
}
```

**Problem**: Sending contract call data to an EOA (Externally Owned Account) is invalid.

**Solution**: Client-side preparation targeting correct contract addresses:
```json
{
  "to": "******************************************",    // Factory Contract (correct!)
  "data": "0x33b9b37309ac8117090c1b5b4f4353078f15c0d1f934e0cfe222e032fe3ad43dffe4efea"
}
```

### 2. Transaction Hash Issues

**Previous Issue**: Transactions appeared successful but hashes couldn't be found on Etherscan.

**Root Cause**: Fake transaction receipts were being returned:
```typescript
// OLD - WRONG
return {
  hash,
  wait: async () => {
    return { status: 1, transactionHash: hash }; // Fake receipt!
  }
};
```

**Solution**: Proper wagmi transaction handling:
```typescript
// NEW - CORRECT
const { data: txHash } = useWriteContract();
const { data: receipt, isSuccess } = useWaitForTransactionReceipt({ hash: txHash });
```

### 3. Authentication Requirements

**Issue**: API endpoints required both Bearer token and x-api-key header.

**Solution**: Made appId parameter required and added proper validation:
```typescript
if (!appId || typeof appId !== 'string' || appId.trim().length === 0) {
  return {
    success: false,
    error: 'Application ID is required for ENS operations',
  };
}

const headers = {
  Authorization: `Bearer ${token}`,
  'x-api-key': appId,
};
```

## Technical Deep Dive

### Contract ABI Integration

The implementation uses migrated contract ABIs from the backend:

```typescript
// Factory Contract ABI (simplified)
export const FACTORY_CONTRACT_ABI = [
  {
    "inputs": [{"internalType": "bytes32", "name": "_parentNode", "type": "bytes32"}],
    "name": "createSubnameRegistrar",
    "outputs": [{"internalType": "address", "name": "", "type": "address"}],
    "stateMutability": "nonpayable",
    "type": "function"
  }
];

// NameWrapper ABI (simplified)
export const NAMEWRAPPER_CONTRACT_ABI = [
  {
    "inputs": [
      {"internalType": "address", "name": "from", "type": "address"},
      {"internalType": "address", "name": "to", "type": "address"},
      {"internalType": "uint256", "name": "id", "type": "uint256"},
      {"internalType": "uint256", "name": "amount", "type": "uint256"},
      {"internalType": "bytes", "name": "data", "type": "bytes"}
    ],
    "name": "safeTransferFrom",
    "outputs": [],
    "stateMutability": "nonpayable",
    "type": "function"
  }
];
```

### Namehash Calculation

ENS uses a hierarchical naming system where each domain is represented by a 32-byte hash:

```typescript
// Example: "test.eth"
// 1. Split into labels: ["test", "eth"]
// 2. Start with zero hash: 0x0000...
// 3. Process from right to left:
//    - hash = keccak256(0x0000... + keccak256("eth"))
//    - hash = keccak256(hash + keccak256("test"))
// 4. Result: 0x9c22ff5f21f0b81b113e63f7db6da94fedef11b2119b4088b89664fb9a3cb658
```

### Transaction Lifecycle Visualization

```
User Action → Prepare Args → Write Contract → Wallet Confirmation → Blockchain → Receipt
     ↓              ↓              ↓               ↓                ↓           ↓
  Click Button → namehash() → writeContract() → User Signs → Miners → Success/Error
```

## Future Enhancements

1. **Gas Estimation**: Add client-side gas estimation using `estimateGas`
2. **Batch Operations**: Support multiple ENS operations in one transaction
3. **Advanced Error Recovery**: Retry mechanisms for failed transactions
4. **Transaction Simulation**: Preview transaction effects before execution
5. **Multi-chain Support**: Extend to other networks beyond Ethereum/Sepolia
6. **Offline Mode**: Cache contract ABIs for offline transaction preparation
