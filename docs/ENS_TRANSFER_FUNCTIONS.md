# ENS Transfer Functions Integration

This document describes the integration of ENS transfer functions using the Crefy Connect backend API endpoints.

## Overview

The ENS Transfer Functions feature provides two main operations:
1. **Registrar Creation** - Create a subname registrar contract for an ENS domain
2. **NameWrapper Transfer** - Transfer wrapped ENS names between addresses

## Backend API Endpoints

### 1. Prepare Registrar Transaction
**Endpoint:** `POST /ens/prepare-registrar`

Prepares a transaction to create a new subname registrar contract for the given ENS name.

**Request:**
```typescript
{
  "ensName": "myplatform.eth",
  "chain": "sepolia"
}
```

**Response:**
```typescript
{
  "success": true,
  "message": "Transaction prepared successfully",
  "data": {
    "to": "******************************************",
    "data": "0x123...abc",
    "value": "0",
    "gas": "500000",
    "from": "******************************************",
    "chainId": 11155111
  }
}
```

### 2. Prepare NameWrapper Transfer
**Endpoint:** `POST /ens/namewrapper/prepare-transfer`

Prepares a safeTransferFrom transaction for transferring wrapped ENS names between addresses.

**Request:**
```typescript
{
  "chain": "sepolia",
  "from": "******************************************",
  "to": "******************************************",
  "id": "1234567890123456789",
  "amount": "1",
  "data": "0x"
}
```

**Response:**
```typescript
{
  "success": true,
  "message": "NameWrapper transfer transaction prepared successfully",
  "data": {
    "to": "******************************************",
    "data": "0x123...abc",
    "value": "0",
    "gas": "50000",
    "chainId": 11155111
  }
}
```

## Frontend Implementation

### Components

#### ENSTransferFunctions Component
Located at: `components/ens/ens-transfer-functions-simple.tsx`

This component provides a user interface for both registrar creation and NameWrapper transfers using wagmi hooks for direct contract interaction.

**Features:**
- Chain selection (Mainnet, Sepolia, Goerli)
- Transfer type selection (Registrar vs NameWrapper)
- Address validation for NameWrapper transfers
- Transaction status tracking
- Error handling and user feedback

**Props:**
```typescript
interface ENSTransferFunctionsProps {
  ensName: string;
  currentOwner: string;
  appId?: string;
  onSuccess?: (txHash: string, transferType: string) => void;
  onError?: (error: string) => void;
  className?: string;
}
```

### API Integration

#### New API Methods
Added to `lib/api.ts`:

```typescript
// Prepare registrar transaction
async prepareRegistrarTransaction(
  data: PrepareRegistrarRequest, 
  token: string, 
  appId?: string
): Promise<ApiResponse<PrepareRegistrarResponse>>

// Prepare NameWrapper transfer
async prepareNameWrapperTransfer(
  data: PrepareNameWrapperTransferRequest, 
  token: string, 
  appId?: string
): Promise<ApiResponse<PrepareNameWrapperTransferResponse>>
```

#### Utility Functions
Added to `lib/ens-utils.ts`:

```typescript
// Execute prepared transaction from backend
async executePreparedTransaction(
  transactionData: {
    to: string;
    data: string;
    value: string;
    gas: string;
    chainId: number;
  },
  customSigner?: ethers.Signer
): Promise<ethers.TransactionResponse>

// Get ENS token ID for NameWrapper
function getENSTokenId(ensName: string): string

// Validate chain name
function isValidChain(chain: string): boolean

// Get chain ID from chain name
function getChainId(chain: string): number
```

## Usage

### Accessing Transfer Functions

1. Navigate to the ENS Integration page
2. Click on the "Transfer Functions" tab
3. Select the desired operation:
   - **Create Registrar**: Creates a subname registrar contract
   - **NameWrapper Transfer**: Transfers wrapped ENS ownership

### Registrar Creation Workflow

1. Select "Create Registrar" operation
2. Choose the blockchain network
3. Click "Create Registrar Contract"
4. Confirm the transaction in your wallet
5. Wait for transaction confirmation

### NameWrapper Transfer Workflow

1. Select "NameWrapper Transfer" operation
2. Choose the blockchain network
3. Enter the new owner's Ethereum address
4. Click "Transfer via NameWrapper"
5. Confirm the transaction in your wallet
6. Wait for transaction confirmation

## Authentication

Both endpoints require:
- **Bearer Token**: User authentication token
- **x-api-key Header**: Application ID for context

Example request headers:
```typescript
{
  'Authorization': `Bearer ${userToken}`,
  'x-api-key': applicationId
}
```

## Error Handling

The implementation includes comprehensive error handling for:
- Invalid ENS name formats
- Invalid Ethereum addresses
- Insufficient gas fees
- User transaction rejection
- Network connectivity issues
- API authentication errors

## Security Considerations

1. **Address Validation**: All Ethereum addresses are validated before use
2. **Owner Verification**: Only current owners can initiate transfers
3. **Transaction Preparation**: Backend prepares transactions to ensure security
4. **Gas Estimation**: Backend provides gas estimates for transactions
5. **Chain Validation**: Only supported chains are allowed

## Testing

Use the development test account:
- **Email**: <EMAIL>
- **Password**: 123456

Test on Sepolia testnet for safe testing without real ETH costs.

## Integration Notes

- The component is dynamically loaded for better performance
- Supports both mainnet and testnet operations
- Integrates with RainbowKit for wallet connectivity
- Uses the platform's design system for consistent UI
- Provides real-time transaction status updates
- Includes transaction hash links to block explorers
