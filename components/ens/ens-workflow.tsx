'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useAccount, useSendTransaction, useWaitForTransactionReceipt } from 'wagmi';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/lib/toast-context';
import { useAuth } from '@/lib/auth-context';
import { apiService } from '@/lib/api';
import { ApplicationWithApiKey } from '@/lib/api';
import { 
  CheckCircleIcon, 
  ArrowRightIcon, 
  WalletIcon, 
  GlobeIcon,
  ArrowUpDownIcon,
  LoaderIcon,
  AlertCircleIcon
} from 'lucide-react';
import { getENSTokenId } from '@/lib/ens-utils';

interface ENSWorkflowProps {
  selectedApplication: ApplicationWithApiKey;
  currentStep: 'enter-ens' | 'transfer-ens' | 'store-registration';
  onSuccess?: (data: any) => void;
  onStepChange?: (step: 'enter-ens' | 'transfer-ens' | 'store-registration' | 'dashboard') => void;
  className?: string;
}

type WorkflowStep = 'register' | 'transfer' | 'store' | 'complete';

interface ENSWorkflowState {
  currentStep: WorkflowStep;
  ensName: string;
  contractAddress: string;
  createdContractAddress?: string; // Contract address created by prepare-registrar
  chain: string;
  registrarTxHash?: string;
  transferTxHash?: string;
  isLoading: boolean;
  error?: string;
  // Add step completion tracking
  registrarCompleted: boolean;
  transferCompleted: boolean;
  storeCompleted: boolean;
}

export function ENSWorkflow({ selectedApplication, currentStep, onSuccess, onStepChange, className = "" }: ENSWorkflowProps) {
  const { address, isConnected } = useAccount();
  const { showToast } = useToast();
  const { token } = useAuth();
  
  const [state, setState] = useState<ENSWorkflowState>({
    currentStep: currentStep === 'enter-ens' ? 'register' : currentStep === 'transfer-ens' ? 'transfer' : 'store',
    ensName: '',
    contractAddress: address || '',
    chain: 'sepolia',
    isLoading: false,
    registrarCompleted: false,
    transferCompleted: false,
    storeCompleted: false
  });

  // Transaction hooks
  const {
    sendTransaction: sendRegistrarTx,
    data: registrarTxHash,
    error: registrarTxError
  } = useSendTransaction();

  const {
    sendTransaction: sendTransferTx,
    data: transferTxHash,
    error: transferTxError
  } = useSendTransaction();

  // Transaction receipt hooks
  const {
    isLoading: isRegistrarPending,
    isSuccess: isRegistrarSuccess,
    isError: isRegistrarError
  } = useWaitForTransactionReceipt({
    hash: registrarTxHash,
  });

  const {
    isLoading: isTransferPending,
    isSuccess: isTransferSuccess,
    isError: isTransferError
  } = useWaitForTransactionReceipt({
    hash: transferTxHash,
  });

  // Update contract address when wallet changes
  useEffect(() => {
    if (address && !state.contractAddress) {
      setState(prev => ({ ...prev, contractAddress: address }));
    }
  }, [address, state.contractAddress]);

  const handleStoreENS = useCallback(async () => {
    if (!token || !state.ensName || !state.createdContractAddress) return;

    setState(prev => ({ ...prev, isLoading: true, error: undefined }));

    try {
      // Step 3: Store ENS registration in backend using the created contract address
      const response = await apiService.registerENSRoot(
        {
          ens_name: state.ensName,
          contractAddress: state.createdContractAddress, // Use the contract address created in step 1
          chain: state.chain,
          isActive: true
        },
        token,
        selectedApplication.appId
      );

      if (!response.success) {
        throw new Error(response.error || 'Failed to store ENS registration');
      }

      setState(prev => ({
        ...prev,
        currentStep: 'complete',
        isLoading: false,
        storeCompleted: true
      }));

      showToast({
        type: 'success',
        title: 'ENS Registration Complete! 🎉',
        description: `${state.ensName} has been successfully registered and linked to ${selectedApplication.name}`
      });

      // Move to dashboard
      onStepChange?.('dashboard');
      onSuccess?.(response.data);

    } catch (error) {
      console.error('Store ENS failed:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to store ENS registration'
      }));
      showToast({
        type: 'error',
        title: 'Storage Failed',
        description: error instanceof Error ? error.message : 'Unknown error occurred'
      });
    }
  }, [token, state.ensName, state.createdContractAddress, state.chain, selectedApplication.appId, selectedApplication.name, showToast, onSuccess, onStepChange]);

  // Handle registrar transaction success
  useEffect(() => {
    if (isRegistrarSuccess && registrarTxHash) {
      setState(prev => ({
        ...prev,
        registrarTxHash: registrarTxHash,
        currentStep: 'transfer',
        isLoading: false,
        registrarCompleted: true
      }));
      showToast({
        type: 'success',
        title: 'Registrar Created',
        description: 'Subname registrar contract created successfully'
      });
      // Move to next step
      onStepChange?.('transfer-ens');
    }
  }, [isRegistrarSuccess, registrarTxHash, showToast, onStepChange]);

  // Handle transfer transaction success
  useEffect(() => {
    if (isTransferSuccess && transferTxHash) {
      setState(prev => ({
        ...prev,
        transferTxHash: transferTxHash,
        currentStep: 'store',
        isLoading: false,
        transferCompleted: true
      }));
      showToast({
        type: 'success',
        title: 'Transfer Complete',
        description: 'ENS name transferred to contract successfully'
      });
      // Move to next step
      onStepChange?.('store-registration');
      // Automatically proceed to store step
      setTimeout(() => handleStoreENS(), 1000);
    }
  }, [isTransferSuccess, transferTxHash, showToast, onStepChange, handleStoreENS]);

  // Handle transaction errors
  useEffect(() => {
    if (isRegistrarError || registrarTxError) {
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: registrarTxError?.message || 'Registrar transaction failed'
      }));
      showToast({
        type: 'error',
        title: 'Transaction Failed',
        description: registrarTxError?.message || 'Registrar transaction failed'
      });
    }
  }, [isRegistrarError, registrarTxError, showToast]);

  useEffect(() => {
    if (isTransferError || transferTxError) {
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: transferTxError?.message || 'Transfer transaction failed'
      }));
      showToast({
        type: 'error',
        title: 'Transaction Failed',
        description: transferTxError?.message || 'Transfer transaction failed'
      });
    }
  }, [isTransferError, transferTxError, showToast]);

  const handleCreateRegistrar = async () => {
    if (!token || !state.ensName || !isConnected) return;

    setState(prev => ({ ...prev, isLoading: true, error: undefined }));

    try {
      // Step 1: Prepare registrar transaction
      const response = await apiService.prepareRegistrarTransaction(
        {
          ensName: state.ensName,
          chain: state.chain,
          from: address!
        },
        token,
        selectedApplication.appId
      );

      if (!response.success || !response.data?.data) {
        throw new Error(response.error || 'Failed to prepare registrar transaction');
      }

      const txData = response.data.data;

      // Store the created contract address from the API response
      const contractAddress = txData.contractAddress || txData.to;
      setState(prev => ({
        ...prev,
        createdContractAddress: contractAddress
      }));

      // Execute transaction using wagmi
      try {
        sendRegistrarTx({
          to: txData.to as `0x${string}`,
          data: txData.data as `0x${string}`,
          value: BigInt(txData.value || '0'),
        });
      } catch (txError) {
        console.error('Transaction execution failed:', txError);
        setState(prev => ({
          ...prev,
          isLoading: false,
          error: 'Failed to execute transaction'
        }));
        showToast({
          type: 'error',
          title: 'Transaction Failed',
          description: 'Failed to execute registrar transaction'
        });
      }

    } catch (error) {
      console.error('Create registrar failed:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to create registrar'
      }));
      showToast({
        type: 'error',
        title: 'Registrar Creation Failed',
        description: error instanceof Error ? error.message : 'Unknown error occurred'
      });
    }
  };

  const handleTransferENS = async () => {
    if (!token || !state.ensName || !isConnected || !state.createdContractAddress) return;

    setState(prev => ({ ...prev, isLoading: true, error: undefined }));

    try {
      const tokenId = getENSTokenId(state.ensName);

      // Step 2: Prepare NameWrapper transfer using the created contract address
      const response = await apiService.prepareNameWrapperTransfer(
        {
          chain: state.chain,
          from: address!,
          to: state.createdContractAddress, // Use the contract address created in step 1
          id: tokenId,
          amount: "1",
          data: "0x"
        },
        token,
        selectedApplication.appId
      );

      if (!response.success || !response.data?.data) {
        throw new Error(response.error || 'Failed to prepare transfer transaction');
      }

      const txData = response.data.data;

      // Execute transfer using wagmi
      try {
        sendTransferTx({
          to: txData.to as `0x${string}`,
          data: txData.data as `0x${string}`,
          value: BigInt(txData.value || '0'),
        });
      } catch (txError) {
        console.error('Transfer transaction execution failed:', txError);
        setState(prev => ({
          ...prev,
          isLoading: false,
          error: 'Failed to execute transfer transaction'
        }));
        showToast({
          type: 'error',
          title: 'Transaction Failed',
          description: 'Failed to execute transfer transaction'
        });
      }

    } catch (error) {
      console.error('Transfer ENS failed:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to transfer ENS'
      }));
      showToast({
        type: 'error',
        title: 'Transfer Failed',
        description: error instanceof Error ? error.message : 'Unknown error occurred'
      });
    }
  };



  const getStepStatus = (step: WorkflowStep) => {
    const stepOrder: WorkflowStep[] = ['register', 'transfer', 'store', 'complete'];
    const currentIndex = stepOrder.indexOf(state.currentStep);
    const stepIndex = stepOrder.indexOf(step);
    
    if (stepIndex < currentIndex) return 'completed';
    if (stepIndex === currentIndex) return 'current';
    return 'pending';
  };

  const isStepLoading = (step: WorkflowStep) => {
    return state.currentStep === step && (
      state.isLoading || 
      (step === 'register' && isRegistrarPending) ||
      (step === 'transfer' && isTransferPending)
    );
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Workflow Progress */}
      <Card className="bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg rounded-2xl">
        <CardHeader>
          <CardTitle className="text-lg font-bold bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent">
            ENS Integration Workflow
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between mb-6">
            {[
              { step: 'register', label: 'Create Registrar', icon: GlobeIcon },
              { step: 'transfer', label: 'Transfer ENS', icon: ArrowUpDownIcon },
              { step: 'store', label: 'Store Registration', icon: WalletIcon },
              { step: 'complete', label: 'Complete', icon: CheckCircleIcon }
            ].map(({ step, label, icon: Icon }, index) => {
              const status = getStepStatus(step as WorkflowStep);
              const isLoading = isStepLoading(step as WorkflowStep);
              
              return (
                <div key={step} className="flex items-center">
                  <div className={`
                    flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all
                    ${status === 'completed' ? 'bg-green-500 border-green-500 text-white' : ''}
                    ${status === 'current' ? 'bg-[#4A148C] border-[#4A148C] text-white' : ''}
                    ${status === 'pending' ? 'bg-gray-100 border-gray-300 text-gray-400' : ''}
                  `}>
                    {isLoading ? (
                      <LoaderIcon className="h-5 w-5 animate-spin" />
                    ) : (
                      <Icon className="h-5 w-5" />
                    )}
                  </div>
                  <div className="ml-3">
                    <p className={`text-sm font-medium ${
                      status === 'completed' ? 'text-green-600' :
                      status === 'current' ? 'text-[#4A148C]' : 'text-gray-400'
                    }`}>
                      {label}
                    </p>
                  </div>
                  {index < 3 && (
                    <ArrowRightIcon className="h-4 w-4 text-gray-300 mx-4" />
                  )}
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Step Content */}
      {currentStep === 'enter-ens' && (
        <Card className="bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg rounded-2xl">
          <CardHeader>
            <CardTitle className="text-lg font-bold text-[#4A148C] flex items-center gap-2">
              <GlobeIcon className="h-5 w-5" />
              Step 3: Enter ENS Name & Create Registrar
            </CardTitle>
            <p className="text-sm text-gray-600 mt-2">
              Enter your ENS name and create a subname registrar contract to enable subname claiming for your users.
            </p>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-4">
              <div>
                <Label htmlFor="ensName" className="text-sm font-medium text-gray-700">
                  ENS Name *
                </Label>
                <Input
                  id="ensName"
                  placeholder="myproject.eth"
                  value={state.ensName}
                  onChange={(e) => setState(prev => ({ ...prev, ensName: e.target.value }))}
                  className="mt-1 border-[#B497D6]/30 focus:border-[#4A148C] focus:ring-[#4A148C]"
                />
                <div className="mt-2 space-y-1">
                  <p className="text-xs text-gray-500">
                    Enter the ENS name you own (e.g., myproject.eth, mydao.eth)
                  </p>
                  <p className="text-xs text-amber-600">
                    ⚠️ You must own this ENS name with the connected wallet
                  </p>
                </div>
              </div>

              <div>
                <Label htmlFor="walletAddress" className="text-sm font-medium text-gray-700">
                  Connected Wallet
                </Label>
                <div className="mt-1 p-3 bg-gradient-to-r from-[#4A148C]/5 to-[#7B1FA2]/5 border border-[#B497D6]/30 rounded-lg">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <code className="text-sm font-mono text-[#4A148C]">
                      {address ? `${address.slice(0, 6)}...${address.slice(-4)}` : 'Not connected'}
                    </code>
                  </div>
                  <p className="text-xs text-gray-600 mt-1">
                    This wallet must own the ENS name you want to register
                  </p>
                </div>
              </div>

              <div>
                <Label htmlFor="chain" className="text-sm font-medium text-gray-700">
                  Blockchain Network *
                </Label>
                <select
                  id="chain"
                  value={state.chain}
                  onChange={(e) => setState(prev => ({ ...prev, chain: e.target.value }))}
                  className="mt-1 w-full px-3 py-2 border border-[#B497D6]/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#4A148C] focus:border-[#4A148C]"
                >
                  <option value="sepolia">Sepolia Testnet</option>
                  <option value="mainnet">Ethereum Mainnet</option>
                </select>
                <p className="text-xs text-gray-500 mt-1">
                  Select the network where your ENS name is registered
                </p>
              </div>
            </div>

            {state.error && (
              <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg">
                <AlertCircleIcon className="h-5 w-5 text-red-500" />
                <p className="text-sm text-red-700">{state.error}</p>
              </div>
            )}

            {/* Validation Summary */}
            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h4 className="text-sm font-medium text-blue-900 mb-2">Before Creating Registrar:</h4>
              <ul className="text-xs text-blue-700 space-y-1">
                <li className="flex items-center gap-2">
                  {state.ensName ? (
                    <CheckCircleIcon className="h-3 w-3 text-green-600" />
                  ) : (
                    <div className="h-3 w-3 rounded-full border border-blue-400" />
                  )}
                  ENS name entered
                </li>
                <li className="flex items-center gap-2">
                  {isConnected ? (
                    <CheckCircleIcon className="h-3 w-3 text-green-600" />
                  ) : (
                    <div className="h-3 w-3 rounded-full border border-blue-400" />
                  )}
                  Wallet connected
                </li>
                <li className="flex items-center gap-2">
                  {state.ensName && state.ensName.endsWith('.eth') ? (
                    <CheckCircleIcon className="h-3 w-3 text-green-600" />
                  ) : (
                    <div className="h-3 w-3 rounded-full border border-blue-400" />
                  )}
                  Valid ENS format (.eth)
                </li>
              </ul>
            </div>

            <Button
              onClick={handleCreateRegistrar}
              disabled={!state.ensName || !isConnected || !state.ensName.endsWith('.eth') || state.isLoading || isRegistrarPending || state.registrarCompleted}
              className="w-full bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] hover:from-[#6A1B9A] hover:to-[#8E24AA] disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {state.registrarCompleted ? (
                <>
                  <CheckCircleIcon className="h-4 w-4 mr-2 text-green-500" />
                  Registrar Created Successfully
                </>
              ) : state.isLoading || isRegistrarPending ? (
                <>
                  <LoaderIcon className="h-4 w-4 mr-2 animate-spin" />
                  Creating Registrar Contract...
                </>
              ) : !state.ensName ? (
                'Enter ENS Name to Continue'
              ) : !isConnected ? (
                'Connect Wallet First'
              ) : !state.ensName.endsWith('.eth') ? (
                'ENS Name Must End with .eth'
              ) : (
                'Create Subname Registrar Contract'
              )}
            </Button>

            {state.registrarCompleted && state.createdContractAddress && (
              <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                <h4 className="text-sm font-medium text-green-900 mb-2">✅ Registrar Created Successfully!</h4>
                <div className="text-xs text-green-700 space-y-1">
                  <p><strong>Contract Address:</strong></p>
                  <code className="block bg-green-100 p-2 rounded text-xs break-all">
                    {state.createdContractAddress}
                  </code>
                  <p className="mt-2">You can now proceed to transfer your ENS name to this contract.</p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {currentStep === 'transfer-ens' && (
        <Card className="bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg rounded-2xl">
          <CardHeader>
            <CardTitle className="text-lg font-bold text-[#4A148C] flex items-center gap-2">
              <ArrowUpDownIcon className="h-5 w-5" />
              Step 4: Transfer ENS to Contract
            </CardTitle>
            <p className="text-sm text-gray-600 mt-2">
              Transfer ownership of your ENS name to the registrar contract to enable subname claiming functionality.
            </p>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h4 className="text-sm font-medium text-blue-900 mb-2">📋 Transfer Overview</h4>
              <p className="text-sm text-blue-700">
                This step transfers your ENS name from your wallet to the registrar contract, enabling users to claim subnames under your domain.
              </p>
            </div>

            <div className="space-y-4">
              <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg">
                <p className="text-sm font-medium text-blue-900 mb-1">ENS Name to Transfer</p>
                <p className="text-xl font-bold text-blue-700">{state.ensName || 'Not specified'}</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
                  <p className="text-sm font-medium text-gray-700 mb-2">From (Current Owner)</p>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <code className="text-sm font-mono text-gray-900">
                      {address ? `${address.slice(0, 6)}...${address.slice(-4)}` : 'Not connected'}
                    </code>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">Your wallet address</p>
                </div>

                <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                  <p className="text-sm font-medium text-green-700 mb-2">To (New Owner)</p>
                  <div className="flex items-center gap-2">
                    <div className={`w-2 h-2 rounded-full ${state.createdContractAddress ? 'bg-green-500' : 'bg-gray-400'}`}></div>
                    <code className="text-sm font-mono text-green-900">
                      {state.createdContractAddress
                        ? `${state.createdContractAddress.slice(0, 6)}...${state.createdContractAddress.slice(-4)}`
                        : 'Waiting for contract...'
                      }
                    </code>
                  </div>
                  <p className="text-xs text-green-600 mt-1">Registrar contract address</p>
                </div>
              </div>

              {/* Prerequisites Check */}
              <div className="p-4 bg-amber-50 border border-amber-200 rounded-lg">
                <h4 className="text-sm font-medium text-amber-900 mb-2">Prerequisites:</h4>
                <ul className="text-xs text-amber-700 space-y-1">
                  <li className="flex items-center gap-2">
                    {state.registrarCompleted ? (
                      <CheckCircleIcon className="h-3 w-3 text-green-600" />
                    ) : (
                      <div className="h-3 w-3 rounded-full border border-amber-400" />
                    )}
                    Registrar contract created
                  </li>
                  <li className="flex items-center gap-2">
                    {state.createdContractAddress ? (
                      <CheckCircleIcon className="h-3 w-3 text-green-600" />
                    ) : (
                      <div className="h-3 w-3 rounded-full border border-amber-400" />
                    )}
                    Contract address available
                  </li>
                  <li className="flex items-center gap-2">
                    {isConnected ? (
                      <CheckCircleIcon className="h-3 w-3 text-green-600" />
                    ) : (
                      <div className="h-3 w-3 rounded-full border border-amber-400" />
                    )}
                    Wallet connected
                  </li>
                </ul>
              </div>
            </div>

            {state.error && (
              <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg">
                <AlertCircleIcon className="h-5 w-5 text-red-500" />
                <p className="text-sm text-red-700">{state.error}</p>
              </div>
            )}

            <Button
              onClick={handleTransferENS}
              disabled={state.isLoading || isTransferPending || !state.createdContractAddress || !state.registrarCompleted || state.transferCompleted}
              className="w-full bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] hover:from-[#6A1B9A] hover:to-[#8E24AA] disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {state.transferCompleted ? (
                <>
                  <CheckCircleIcon className="h-4 w-4 mr-2 text-green-500" />
                  ENS Transfer Completed Successfully
                </>
              ) : state.isLoading || isTransferPending ? (
                <>
                  <LoaderIcon className="h-4 w-4 mr-2 animate-spin" />
                  Transferring ENS to Contract...
                </>
              ) : !state.registrarCompleted ? (
                'Complete Step 3: Create Registrar First'
              ) : !state.createdContractAddress ? (
                'Waiting for Contract Address...'
              ) : (
                'Transfer ENS Name to Contract'
              )}
            </Button>

            {state.transferCompleted && state.transferTxHash && (
              <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                <h4 className="text-sm font-medium text-green-900 mb-2">✅ Transfer Completed Successfully!</h4>
                <div className="text-xs text-green-700 space-y-1">
                  <p><strong>Transaction Hash:</strong></p>
                  <code className="block bg-green-100 p-2 rounded text-xs break-all">
                    {state.transferTxHash}
                  </code>
                  <p className="mt-2">Your ENS name has been transferred to the registrar contract. Proceeding to store registration...</p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {currentStep === 'store-registration' && (
        <Card className="bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg rounded-2xl">
          <CardHeader>
            <CardTitle className="text-lg font-bold text-[#4A148C] flex items-center gap-2">
              <WalletIcon className="h-5 w-5" />
              Step 5: Store Registration
            </CardTitle>
            <p className="text-sm text-gray-600 mt-2">
              Save your ENS registration details in the Crefy Connect backend to complete the integration.
            </p>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h4 className="text-sm font-medium text-blue-900 mb-2">📝 Registration Summary</h4>
              <div className="text-sm text-blue-700 space-y-2">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <p className="font-medium">ENS Name:</p>
                    <p className="font-mono text-xs bg-blue-100 p-1 rounded">{state.ensName}</p>
                  </div>
                  <div>
                    <p className="font-medium">Application:</p>
                    <p className="text-xs">{selectedApplication.name}</p>
                  </div>
                  <div>
                    <p className="font-medium">Contract Address:</p>
                    <p className="font-mono text-xs bg-blue-100 p-1 rounded break-all">
                      {state.createdContractAddress || 'Not available'}
                    </p>
                  </div>
                  <div>
                    <p className="font-medium">Network:</p>
                    <p className="text-xs capitalize">{state.chain}</p>
                  </div>
                </div>
              </div>
            </div>

            {state.isLoading ? (
              <div className="flex flex-col items-center justify-center py-8 space-y-4">
                <LoaderIcon className="h-8 w-8 animate-spin text-[#4A148C]" />
                <div className="text-center">
                  <p className="text-sm font-medium text-gray-900">Storing Registration...</p>
                  <p className="text-xs text-gray-500 mt-1">
                    Saving your ENS integration details to Crefy Connect backend
                  </p>
                </div>
              </div>
            ) : state.storeCompleted ? (
              <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                <h4 className="text-sm font-medium text-green-900 mb-2">✅ Registration Stored Successfully!</h4>
                <p className="text-sm text-green-700">
                  Your ENS integration has been completed and saved. You can now manage your ENS domain from the dashboard.
                </p>
              </div>
            ) : (
              <div className="p-4 bg-amber-50 border border-amber-200 rounded-lg">
                <h4 className="text-sm font-medium text-amber-900 mb-2">⏳ Ready to Store Registration</h4>
                <p className="text-sm text-amber-700">
                  All previous steps completed successfully. The registration will be stored automatically.
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {state.currentStep === 'complete' && (
        <Card className="bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg rounded-2xl">
          <CardHeader>
            <CardTitle className="text-lg font-bold text-green-600 flex items-center gap-2">
              <CheckCircleIcon className="h-5 w-5" />
              ENS Integration Complete! 🎉
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
              <p className="text-sm text-green-700">
                Your ENS name <strong>{state.ensName}</strong> has been successfully integrated with {selectedApplication.name}.
                Users can now claim subnames under your domain!
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <p className="font-medium text-gray-700">Registrar Transaction:</p>
                <p className="text-gray-600 break-all">{state.registrarTxHash}</p>
              </div>
              <div>
                <p className="font-medium text-gray-700">Transfer Transaction:</p>
                <p className="text-gray-600 break-all">{state.transferTxHash}</p>
              </div>
            </div>

            <Button
              onClick={() => window.location.reload()}
              className="w-full bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] hover:from-[#6A1B9A] hover:to-[#8E24AA]"
            >
              View ENS Dashboard
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
